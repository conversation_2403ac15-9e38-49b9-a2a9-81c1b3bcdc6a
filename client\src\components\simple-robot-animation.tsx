import React, { useEffect, useRef, useState } from 'react';
import p5 from 'p5';

interface SimpleRobotAnimationProps {
  state?: 'idle' | 'talk' | 'dance' | 'roll' | 'search';
  scale?: number;
  className?: string;
  onRobotClick?: () => void;
}

export const SimpleRobotAnimation: React.FC<SimpleRobotAnimationProps> = ({
  state = 'idle',
  scale = 0.5,
  className = '',
  onRobotClick
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const p5InstanceRef = useRef<p5 | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    if (!containerRef.current) return;

    const sketch = (p: p5) => {
      // Animation state variables
      let robotState = 'idle';
      let stateStartTime = 0;
      
      // Position and movement variables
      let robotX = 0;
      let robotY = 0;
      
      // Animation effect variables
      let headBobAmount = 0;
      let talkPulse = 0;
      let dancePhase = 0;
      let searchAngle = 0;
      
      // Scale-related variables
      let robotScale = scale;
      let targetScale = scale;
      
      // Centering and position variables
      let centerX = 0;
      let centerY = 0;
      let danceStartX = 0;
      let danceStartY = 0;
      
      // Constants
      const SCALE_TRANSITION_SPEED = 0.1;

      p.setup = () => {
        const canvas = p.createCanvas(400, 300);
        canvas.parent(containerRef.current!);
        
        // Initialize robot position to center of canvas
        centerX = p.width / 2;
        centerY = p.height / 2;
        robotX = centerX;
        robotY = centerY;
        
        // Set initial state
        setRobotState(state);
        setIsLoaded(true);
      };

      p.draw = () => {
        // Clear background with transparent
        p.clear();
        
        // Update animation based on current state
        updateAnimation();
        
        // Draw robot
        drawRobot();
      };

      const updateAnimation = () => {
        let currentTime = p.millis();
        let timeInState = currentTime - stateStartTime;

        // Handle smooth scale transitions
        if (p.abs(robotScale - targetScale) > 0.01) {
          robotScale = p.lerp(robotScale, targetScale, SCALE_TRANSITION_SPEED);
        } else {
          robotScale = targetScale;
        }

        // State-specific updates
        switch (robotState) {
          case 'idle':
            // Subtle bobbing motion
            headBobAmount = p.sin(currentTime * 0.002) * 5;
            break;

          case 'talk':
            // Pulsing head effect
            talkPulse = p.sin(currentTime * 0.01) * 0.05;
            headBobAmount = p.sin(currentTime * 0.01) * 3;
            break;

          case 'dance':
            dancePhase += 0.05;
            headBobAmount = p.sin(dancePhase * 2) * 8;
            
            robotX = danceStartX + p.sin(dancePhase) * 30;
            robotY = danceStartY + p.sin(dancePhase * 0.5) * 10;
            break;

          case 'search':
            searchAngle += 0.03;
            headBobAmount = p.sin(currentTime * 0.005) * 3;
            break;

          case 'roll':
            headBobAmount = p.sin(currentTime * 0.01) * 3;
            break;
        }
      };

      const drawRobot = () => {
        p.push();
        p.translate(robotX, robotY);

        // Draw shadow
        drawShadow();

        // Apply bobbing effect
        p.translate(0, headBobAmount);

        // Scale the robot
        p.scale(robotScale);

        // Draw simple robot shape
        p.fill(100, 150, 255);
        p.stroke(50, 100, 200);
        p.strokeWeight(2);
        
        // Apply talk pulsing effect
        if (robotState === 'talk') {
          p.scale(1 + talkPulse);
        }
        
        // Body
        p.ellipse(0, 0, 80, 100);
        
        // Head
        p.ellipse(0, -40, 50, 50);
        
        // Eyes
        p.fill(255);
        p.noStroke();
        p.ellipse(-10, -45, 8, 8);
        p.ellipse(10, -45, 8, 8);
        
        // Pupils (animated based on state)
        p.fill(0);
        let pupilOffsetX = 0;
        let pupilOffsetY = 0;
        
        if (robotState === 'search') {
          pupilOffsetX = p.sin(searchAngle) * 2;
          pupilOffsetY = p.cos(searchAngle * 0.5) * 1;
        }
        
        p.ellipse(-10 + pupilOffsetX, -45 + pupilOffsetY, 4, 4);
        p.ellipse(10 + pupilOffsetX, -45 + pupilOffsetY, 4, 4);
        
        // Mouth (changes based on state)
        p.stroke(50, 100, 200);
        p.strokeWeight(2);
        p.noFill();
        
        if (robotState === 'talk') {
          // Animated mouth for talking
          let mouthSize = 3 + p.sin(p.millis() * 0.02) * 2;
          p.ellipse(0, -30, mouthSize, mouthSize);
        } else if (robotState === 'dance') {
          // Happy mouth for dancing
          p.arc(0, -30, 15, 10, 0, p.PI);
        } else {
          // Neutral mouth
          p.line(-5, -30, 5, -30);
        }
        
        // Arms (animated based on state)
        p.stroke(50, 100, 200);
        p.strokeWeight(3);
        
        if (robotState === 'dance') {
          // Dancing arms
          let leftArmAngle = p.sin(dancePhase) * 0.5;
          let rightArmAngle = p.sin(dancePhase + p.PI) * 0.5;
          
          p.push();
          p.translate(-30, -10);
          p.rotate(leftArmAngle);
          p.line(0, 0, 0, 25);
          p.pop();
          
          p.push();
          p.translate(30, -10);
          p.rotate(rightArmAngle);
          p.line(0, 0, 0, 25);
          p.pop();
        } else {
          // Normal arms
          p.line(-30, -10, -30, 15);
          p.line(30, -10, 30, 15);
        }

        p.pop();
      };

      const drawShadow = () => {
        p.push();
        p.translate(0, 60);
        p.fill(0, 0, 0, 50);
        p.noStroke();
        p.ellipse(0, 0, 120 * robotScale, 30 * robotScale * 0.2);
        p.pop();
      };

      const setRobotState = (newState: string) => {
        robotState = newState;
        stateStartTime = p.millis();

        switch (newState) {
          case 'talk':
            talkPulse = 0;
            break;
          case 'dance':
            dancePhase = 0;
            danceStartX = robotX;
            danceStartY = robotY;
            break;
          case 'search':
            searchAngle = 0;
            break;
        }
      };

      p.mousePressed = () => {
        if (onRobotClick) {
          const d = p.dist(p.mouseX, p.mouseY, robotX, robotY);
          if (d < 100 * robotScale) {
            onRobotClick();
          }
        }
      };

      // Update robot state when prop changes
      p.updateRobotState = (newState: string) => {
        setRobotState(newState);
      };

      // Update robot scale when prop changes
      p.updateRobotScale = (newScale: number) => {
        targetScale = newScale;
      };
    };

    p5InstanceRef.current = new p5(sketch);

    return () => {
      if (p5InstanceRef.current) {
        p5InstanceRef.current.remove();
        p5InstanceRef.current = null;
      }
    };
  }, []);

  // Update robot state when prop changes
  useEffect(() => {
    if (p5InstanceRef.current && (p5InstanceRef.current as any).updateRobotState) {
      (p5InstanceRef.current as any).updateRobotState(state);
    }
  }, [state]);

  // Update robot scale when prop changes
  useEffect(() => {
    if (p5InstanceRef.current && (p5InstanceRef.current as any).updateRobotScale) {
      (p5InstanceRef.current as any).updateRobotScale(scale);
    }
  }, [scale]);

  return (
    <div className={`relative ${className}`}>
      {!isLoaded && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-sm text-muted-foreground">Loading Daswos Robot...</div>
        </div>
      )}
      <div ref={containerRef} className="w-full h-full" />
    </div>
  );
};

export default SimpleRobotAnimation;
