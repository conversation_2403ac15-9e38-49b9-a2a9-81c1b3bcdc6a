import React, { useEffect, useRef, useState } from 'react';
import p5 from 'p5';

interface DaswosRobotAnimationProps {
  state?: 'idle' | 'talk' | 'dance' | 'roll' | 'search';
  scale?: number;
  className?: string;
  onRobotClick?: () => void;
}

export const DaswosRobotAnimation: React.FC<DaswosRobotAnimationProps> = ({
  state = 'idle',
  scale = 0.5,
  className = '',
  onRobotClick
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const p5InstanceRef = useRef<p5 | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [imagesLoaded, setImagesLoaded] = useState(false);

  useEffect(() => {
    if (!containerRef.current) return;

    const sketch = (p: p5) => {
      // Image variables for different robot views
      let robotImages: { [key: string]: p5.Image } = {};

      // Animation state variables
      let robotState = 'idle';
      let stateStartTime = 0;

      // Position and movement variables
      let robotX = 0;
      let robotY = 0;
      let targetX = 0;
      let targetY = 0;
      let isRolling = false;
      let rollDirection = 0;
      let rollSpeed = 0;

      // Animation effect variables
      let headRotation = 0;
      let headBobAmount = 0;
      let bodyRotation = 0;
      let bodyRotationSpeed = 0;
      let armLeftRotation = 0;
      let armRightRotation = 0;
      let eyeBlinkTime = 0;
      let isBlinking = false;
      let talkPulse = 0;
      let dancePhase = 0;
      let searchAngle = 0;

      // View management variables
      let currentView = 'front';
      let targetView = 'front';

      // Scale-related variables
      let robotScale = scale;
      let targetScale = scale;

      // Centering and position variables
      let centerX = 0;
      let centerY = 0;
      let shouldReturnToCenter = false;
      let danceStartX = 0;
      let danceStartY = 0;

      // Constants
      const TRANSITION_DURATION = 500;
      const SHADOW_OPACITY = 0.3;
      const SHADOW_SCALE_Y = 0.2;
      const SHADOW_OFFSET_Y = 20;
      const SCALE_TRANSITION_SPEED = 0.1;
      const POSITION_TRANSITION_SPEED = 0.05;

      p.preload = () => {
        // Load all robot view images with error handling
        try {
          robotImages.front = p.loadImage('/images/robot/robot_front_view.png',
            () => console.log('Front image loaded'),
            () => console.error('Failed to load front image')
          );
          robotImages.side = p.loadImage('/images/robot/robot_side_view.png',
            () => console.log('Side image loaded'),
            () => console.error('Failed to load side image')
          );
          robotImages.threeQuarter = p.loadImage('/images/robot/robot_three_quarter_view.png',
            () => console.log('Three quarter image loaded'),
            () => console.error('Failed to load three quarter image')
          );
          robotImages.back = p.loadImage('/images/robot/robot_back_view.png',
            () => console.log('Back image loaded'),
            () => console.error('Failed to load back image')
          );
          robotImages.top = p.loadImage('/images/robot/robot_top_view.png',
            () => console.log('Top image loaded'),
            () => console.error('Failed to load top image')
          );
        } catch (error) {
          console.error('Error loading robot images:', error);
        }
      };

      p.setup = () => {
        const canvas = p.createCanvas(400, 300);
        canvas.parent(containerRef.current!);

        // Initialize robot position to center of canvas
        centerX = p.width / 2;
        centerY = p.height / 2;
        robotX = centerX;
        robotY = centerY;

        // Set initial state
        setRobotState(state);

        // Check if images loaded
        console.log('Robot images loaded:', Object.keys(robotImages).map(key => ({
          key,
          loaded: robotImages[key] && robotImages[key].width > 0
        })));

        setIsLoaded(true);
      };

      p.draw = () => {
        // Clear background with transparent
        p.clear();

        // Update animation based on current state
        updateAnimation();

        // Draw robot
        drawRobot();
      };

      const updateAnimation = () => {
        let currentTime = p.millis();
        let timeInState = currentTime - stateStartTime;

        // Handle smooth scale transitions
        if (p.abs(robotScale - targetScale) > 0.01) {
          robotScale = p.lerp(robotScale, targetScale, SCALE_TRANSITION_SPEED);
        } else {
          robotScale = targetScale;
        }

        // State-specific updates
        switch (robotState) {
          case 'idle':
            // Subtle bobbing motion
            headBobAmount = p.sin(currentTime * 0.002) * 5;
            headRotation = p.sin(currentTime * 0.001) * 0.1;
            armLeftRotation = p.sin(currentTime * 0.001) * 0.05;
            armRightRotation = p.sin(currentTime * 0.001 + p.PI) * 0.05;

            // Occasional eye blink
            if (currentTime > eyeBlinkTime && !isBlinking) {
              isBlinking = true;
              eyeBlinkTime = currentTime + 200;
            } else if (currentTime > eyeBlinkTime && isBlinking) {
              isBlinking = false;
              eyeBlinkTime = currentTime + p.random(2000, 5000);
            }

            targetView = 'front';
            break;

          case 'talk':
            // Pulsing head effect
            talkPulse = p.sin(currentTime * 0.01) * 0.05;
            headBobAmount = p.sin(currentTime * 0.01) * 3;
            armLeftRotation = p.sin(currentTime * 0.008) * 0.2;
            armRightRotation = p.sin(currentTime * 0.008 + p.PI) * 0.2;
            targetView = 'front';
            break;

          case 'dance':
            dancePhase += 0.05;
            headBobAmount = p.sin(dancePhase * 2) * 8;
            headRotation = p.sin(dancePhase) * 0.2;
            armLeftRotation = p.sin(dancePhase) * 0.4;
            armRightRotation = p.sin(dancePhase + p.PI) * 0.4;

            if (!isRolling) {
              robotX = danceStartX + p.sin(dancePhase) * 30;
              robotY = danceStartY + p.sin(dancePhase * 0.5) * 10;
            }

            // Cycle through views for dancing
            if (timeInState % 2000 < 500) {
              targetView = 'front';
            } else if (timeInState % 2000 < 1000) {
              targetView = 'threeQuarter';
            } else if (timeInState % 2000 < 1500) {
              targetView = 'side';
            } else {
              targetView = 'threeQuarter';
            }
            break;

          case 'search':
            searchAngle += 0.03;
            headRotation = p.sin(searchAngle) * 0.3;
            headBobAmount = p.sin(currentTime * 0.005) * 3;
            armLeftRotation = p.sin(searchAngle * 0.5) * 0.2;
            armRightRotation = p.sin(searchAngle * 0.5 + p.PI) * 0.2;

            // Cycle through views for searching
            if (timeInState % 3000 < 1000) {
              targetView = 'front';
            } else if (timeInState % 3000 < 2000) {
              targetView = 'threeQuarter';
            } else {
              targetView = 'side';
            }
            break;

          case 'roll':
            headBobAmount = p.sin(currentTime * 0.01) * 3;
            armLeftRotation = p.sin(currentTime * 0.01) * 0.1;
            armRightRotation = p.sin(currentTime * 0.01 + p.PI) * 0.1;
            break;
        }
      };

      const drawRobot = () => {
        p.push();
        p.translate(robotX, robotY);

        // Draw shadow
        drawShadow();

        // Apply bobbing effect
        p.translate(0, headBobAmount);

        // Scale the robot
        p.scale(robotScale);

        // Apply body rotation for wheel effect
        if (robotState === 'roll') {
          p.push();
          p.rotate(bodyRotation);
        }

        // Determine which image to draw based on current view
        let currentImage = robotImages[currentView];

        if (currentImage && currentImage.width > 0) {
          // Apply effects based on state
          if (robotState === 'talk') {
            p.scale(1 + talkPulse);
          }

          // Draw the current view
          p.image(currentImage, -currentImage.width/2, -currentImage.height/2);
        } else {
          // Fallback: draw a simple robot shape
          p.fill(100, 150, 255);
          p.stroke(50, 100, 200);
          p.strokeWeight(2);

          // Body
          p.ellipse(0, 0, 80 * robotScale, 100 * robotScale);

          // Head
          p.ellipse(0, -40 * robotScale, 50 * robotScale, 50 * robotScale);

          // Eyes
          p.fill(255);
          p.noStroke();
          p.ellipse(-10 * robotScale, -45 * robotScale, 8 * robotScale, 8 * robotScale);
          p.ellipse(10 * robotScale, -45 * robotScale, 8 * robotScale, 8 * robotScale);

          // Pupils
          p.fill(0);
          p.ellipse(-10 * robotScale, -45 * robotScale, 4 * robotScale, 4 * robotScale);
          p.ellipse(10 * robotScale, -45 * robotScale, 4 * robotScale, 4 * robotScale);
        }

        if (robotState === 'roll') {
          p.pop();
        }

        p.pop();
      };

      const drawShadow = () => {
        p.push();
        p.translate(0, SHADOW_OFFSET_Y);
        p.fill(0, 0, 0, SHADOW_OPACITY * 255);
        p.noStroke();
        p.ellipse(0, 0, 120 * robotScale, 30 * robotScale * SHADOW_SCALE_Y);
        p.pop();
      };

      const setRobotState = (newState: string) => {
        robotState = newState;
        stateStartTime = p.millis();

        switch (newState) {
          case 'idle':
            targetView = 'front';
            break;
          case 'talk':
            targetView = 'front';
            talkPulse = 0;
            break;
          case 'dance':
            dancePhase = 0;
            danceStartX = robotX;
            danceStartY = robotY;
            break;
          case 'search':
            searchAngle = 0;
            break;
          case 'roll':
            isRolling = true;
            break;
        }
      };

      p.mousePressed = () => {
        if (onRobotClick) {
          const d = p.dist(p.mouseX, p.mouseY, robotX, robotY);
          if (d < 100 * robotScale) {
            onRobotClick();
          }
        }
      };

      // Update robot state when prop changes
      p.updateRobotState = (newState: string) => {
        setRobotState(newState);
      };

      // Update robot scale when prop changes
      p.updateRobotScale = (newScale: number) => {
        targetScale = newScale;
      };
    };

    p5InstanceRef.current = new p5(sketch);

    return () => {
      if (p5InstanceRef.current) {
        p5InstanceRef.current.remove();
        p5InstanceRef.current = null;
      }
    };
  }, []);

  // Update robot state when prop changes
  useEffect(() => {
    if (p5InstanceRef.current && (p5InstanceRef.current as any).updateRobotState) {
      (p5InstanceRef.current as any).updateRobotState(state);
    }
  }, [state]);

  // Update robot scale when prop changes
  useEffect(() => {
    if (p5InstanceRef.current && (p5InstanceRef.current as any).updateRobotScale) {
      (p5InstanceRef.current as any).updateRobotScale(scale);
    }
  }, [scale]);

  return (
    <div className={`relative ${className}`}>
      {!isLoaded && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-sm text-muted-foreground">Loading Daswos Robot...</div>
        </div>
      )}
      <div ref={containerRef} className="w-full h-full" />
    </div>
  );
};

export default DaswosRobotAnimation;
