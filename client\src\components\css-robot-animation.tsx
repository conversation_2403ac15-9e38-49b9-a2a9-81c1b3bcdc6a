import React, { useEffect, useState } from 'react';

interface CSSRobotAnimationProps {
  state?: 'idle' | 'talk' | 'dance' | 'roll' | 'search';
  scale?: number;
  className?: string;
  onRobotClick?: () => void;
}

export const CSSRobotAnimation: React.FC<CSSRobotAnimationProps> = ({
  state = 'idle',
  scale = 0.5,
  className = '',
  onRobotClick
}) => {
  const [currentState, setCurrentState] = useState(state);

  useEffect(() => {
    setCurrentState(state);
  }, [state]);

  const getAnimationClass = () => {
    switch (currentState) {
      case 'talk':
        return 'animate-pulse';
      case 'dance':
        return 'animate-bounce';
      case 'search':
        return 'animate-spin';
      case 'roll':
        return 'animate-ping';
      default:
        return 'animate-pulse';
    }
  };

  const getRobotColor = () => {
    switch (currentState) {
      case 'talk':
        return 'text-blue-500';
      case 'dance':
        return 'text-purple-500';
      case 'search':
        return 'text-green-500';
      case 'roll':
        return 'text-orange-500';
      default:
        return 'text-blue-400';
    }
  };

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <div 
        className={`cursor-pointer transition-all duration-300 ${getAnimationClass()} ${getRobotColor()}`}
        style={{ transform: `scale(${scale})` }}
        onClick={onRobotClick}
      >
        <svg
          width="200"
          height="240"
          viewBox="0 0 200 240"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="drop-shadow-lg"
        >
          {/* Robot Shadow */}
          <ellipse
            cx="100"
            cy="220"
            rx="60"
            ry="12"
            fill="currentColor"
            opacity="0.2"
          />
          
          {/* Robot Body */}
          <ellipse
            cx="100"
            cy="140"
            rx="40"
            ry="50"
            fill="currentColor"
            stroke="currentColor"
            strokeWidth="2"
            opacity="0.8"
          />
          
          {/* Robot Head */}
          <circle
            cx="100"
            cy="80"
            r="25"
            fill="currentColor"
            stroke="currentColor"
            strokeWidth="2"
            opacity="0.9"
          />
          
          {/* Eyes */}
          <circle
            cx="90"
            cy="75"
            r="4"
            fill="white"
          />
          <circle
            cx="110"
            cy="75"
            r="4"
            fill="white"
          />
          
          {/* Eye pupils */}
          <circle
            cx={currentState === 'search' ? "88" : "90"}
            cy={currentState === 'search' ? "73" : "75"}
            r="2"
            fill="black"
            className={currentState === 'search' ? 'animate-ping' : ''}
          />
          <circle
            cx={currentState === 'search' ? "112" : "110"}
            cy={currentState === 'search' ? "73" : "75"}
            r="2"
            fill="black"
            className={currentState === 'search' ? 'animate-ping' : ''}
          />
          
          {/* Mouth */}
          {currentState === 'talk' ? (
            <ellipse
              cx="100"
              cy="90"
              rx="3"
              ry="5"
              fill="white"
              className="animate-pulse"
            />
          ) : currentState === 'dance' ? (
            <path
              d="M 90 90 Q 100 95 110 90"
              stroke="white"
              strokeWidth="2"
              fill="none"
            />
          ) : (
            <line
              x1="95"
              y1="90"
              x2="105"
              y2="90"
              stroke="white"
              strokeWidth="2"
            />
          )}
          
          {/* Arms */}
          <line
            x1="70"
            y1="120"
            x2="70"
            y2={currentState === 'dance' ? "135" : "145"}
            stroke="currentColor"
            strokeWidth="3"
            className={currentState === 'dance' ? 'animate-bounce' : ''}
          />
          <line
            x1="130"
            y1="120"
            x2="130"
            y2={currentState === 'dance' ? "135" : "145"}
            stroke="currentColor"
            strokeWidth="3"
            className={currentState === 'dance' ? 'animate-bounce' : ''}
          />
          
          {/* Antenna */}
          <line
            x1="100"
            y1="55"
            x2="100"
            y2="45"
            stroke="currentColor"
            strokeWidth="2"
          />
          <circle
            cx="100"
            cy="45"
            r="3"
            fill="currentColor"
            className={currentState === 'search' ? 'animate-ping' : currentState === 'talk' ? 'animate-pulse' : ''}
          />
        </svg>
        
        {/* State indicator */}
        <div className="text-center mt-2">
          <div className="text-xs font-medium opacity-70">
            {currentState === 'idle' && 'Ready'}
            {currentState === 'talk' && 'Speaking...'}
            {currentState === 'dance' && 'Dancing!'}
            {currentState === 'search' && 'Thinking...'}
            {currentState === 'roll' && 'Moving...'}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CSSRobotAnimation;
